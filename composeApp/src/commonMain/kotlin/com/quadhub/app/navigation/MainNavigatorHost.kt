package com.quadhub.app.navigation

import androidx.navigation.NavGraphBuilder
import com.quadhub.core.presentation.feature.FeatureLauncher
import com.quadhub.core.presentation.navigation.NavigationDispatcher
import com.quadhub.core.presentation.navigation.compose.NavigatorHost
import me.tatarka.inject.annotations.Inject
import kotlin.jvm.JvmSuppressWildcards

@Inject
internal class MainNavigatorHost constructor(
    private val launcher: Map<String, @JvmSuppressWildcards FeatureLauncher>
) : NavigatorHost.Root {

    override fun buildGraph(builder: NavGraphBuilder, navigationDispatcher: NavigationDispatcher) {
        launcher
            .map { launcher -> launcher.value }
            .map { navigationHost -> navigationHost.navigationGraph() }
            .onEach { navigationHost: NavigatorHost.Child ->
                navigationHost.buildGraph(
                    builder,
                    navigationDispatcher
                )
            }
    }

}