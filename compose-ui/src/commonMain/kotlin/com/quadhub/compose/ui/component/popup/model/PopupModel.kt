package com.quadhub.compose.ui.component.popup.model

import androidx.compose.runtime.Immutable
import com.quadhub.compose.ui.component.button.model.ButtonModel
import com.quadhub.compose.ui.element.image.Image
import com.quadhub.compose.ui.element.text.Clause


@Immutable
data class PopupModel(
    val headerImage: Image? = null,
    val title: Clause,
    val subtitle: Clause,
    val dismissible: Boolean = true,
    val primaryBtn: ButtonModel? = null,
    val secondaryBtn: ButtonModel? = null,
)

enum class PopupEventType {
    Primary, Secondary, Dismiss
}
