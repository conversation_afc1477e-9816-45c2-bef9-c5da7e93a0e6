package com.quadhub.feature.auth.ui.screens.dispatch

import com.quadhub.core.presentation.mvi.Reducer
import me.tatarka.inject.annotations.Inject
import com.quadhub.feature.auth.ui.screens.dispatch.DispatchContract.ViewState
import com.quadhub.feature.auth.ui.screens.dispatch.DispatchContract.Result

internal class DispatchReducer @Inject constructor() : Reducer<ViewState, Result> {
    override fun reduce(state: ViewState, result: Result): ViewState {
        return when (result) {
            is Result.OnNavigationCommandUpdated -> state.copy(navigationCommand = result.navigationCommand)
        }
    }
}