package com.quadhub.feature.auth.ui.screens.dispatch

import com.quadhub.core.presentation.mvi.Interactor
import com.quadhub.core.presentation.navigation.NavigationCommand
import me.tatarka.inject.annotations.Inject
import com.quadhub.feature.auth.ui.screens.dispatch.DispatchContract.Intent
import com.quadhub.feature.auth.ui.screens.dispatch.DispatchContract.Mapper
import com.quadhub.feature.auth.ui.screens.dispatch.DispatchContract.Result
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.merge

internal class DispatchInteractor @Inject constructor(
    private val mapper: Mapper,
) : Interactor<Intent, Result> {

    override fun process(intents: Flow<Intent>): Flow<Result> {
        return merge(
            intents.filterIsInstance<Intent.OnGetStartButtonClick>().map {
                Result.OnNavigationCommandUpdated(
                    navigationCommand = NavigationCommand.FeatureCommand("signup")
                )
            },
            intents.filterIsInstance<Intent.Navigation>().map(mapper::toResult)
        )
    }
}