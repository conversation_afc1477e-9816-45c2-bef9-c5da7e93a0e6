package com.quadhub.feature.auth.ui.screens.dispatch

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.windowInsetsBottomHeight
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.quadhub.compose.ui.component.scaffold.Scaffold
import com.quadhub.compose.ui.element.image.Image
import com.quadhub.compose.ui.element.text.Text
import com.quadhub.compose.ui.element.text.WithTextClickHandler
import com.quadhub.compose.ui.theme.QuadhubColors
import com.quadhub.compose.ui.theme.Theme
import com.quadhub.core.presentation.navigation.NavigationDispatcher
import com.quadhub.feature.auth.ui.screens.dispatch.DispatchContract.Intent

@Composable
internal fun DispatchScreen(
    viewModel: DispatchViewModel,
    navigationDispatcher: NavigationDispatcher
) {

    val viewState by remember { derivedStateOf { viewModel.state.value } }

    viewState.navigationCommand?.let {
        navigationDispatcher.navigateTo(it)
        viewModel.emit(Intent.Navigation.Done)
    }

    Scaffold(contentWindowInsets = WindowInsets.statusBars) { paddingValues ->
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(Theme.spacing.x2),
            modifier = Modifier.padding(paddingValues).padding(horizontal = Theme.spacing.x4)
        ) {
            Image(image = viewState.headerImage, modifier = Modifier.weight(1f))
            Spacer(Modifier.height(Theme.spacing.x6))
            Text(
                clause = viewState.title,
                modifier = Modifier.fillMaxWidth(),
                style = Theme.typography.headingM
            )
            Spacer(Modifier.height(Theme.spacing.x2))
            Text(
                clause = viewState.subtitle,
                style = Theme.typography.body.copy(color = QuadhubColors.Secondary.NeutralOne)
            )
            Spacer(Modifier.height(Theme.spacing.x8))

            Text(
                clause = viewState.socialIntroClause,
                style = Theme.typography.bodyS.copy(color = QuadhubColors.Secondary.NeutralOne)
            )
            Spacer(Modifier.height(Theme.spacing.x4))
            WithTextClickHandler(onTextActionClick = { actionId ->
                viewModel.emit(Intent.SignInTextActionClick(actionId = actionId))
            }) {
                Text(
                    clause = viewState.footerClause,
                    style = Theme.typography.bodyS.copy(color = QuadhubColors.Secondary.NeutralOne)
                )
            }
            Spacer(Modifier.height(Theme.spacing.x1))
            Spacer(Modifier.windowInsetsBottomHeight(WindowInsets.navigationBars))
        }

    }

}