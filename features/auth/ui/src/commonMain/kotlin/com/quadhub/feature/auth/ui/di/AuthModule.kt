package com.quadhub.feature.auth.ui.di

import com.quadhub.core.presentation.feature.FeatureLauncher
import com.quadhub.feature.auth.ui.launcher.AuthFeatureLauncher
import com.quadhub.feature.auth.ui.screens.dispatch.di.DispatchUiComponent
import com.quadhub.feature.auth.ui.screens.dispatch.di.create
import me.tatarka.inject.annotations.Component
import me.tatarka.inject.annotations.IntoMap
import me.tatarka.inject.annotations.Provides

@Component
abstract class AuthModule {
    abstract val features: Map<String, FeatureLauncher>

    @Provides
    @IntoMap
    protected fun provideFeatureLauncher(launcher: FeatureLauncher): Pair<String, FeatureLauncher> =
        AuthFeatureLauncher.FEATURE_NAME to launcher


    @Provides
    internal fun provideAuthFeatureLauncher(
    ): FeatureLauncher = AuthFeatureLauncher(
        viewModelProviders = AuthViewModelComponent::class.create(
            dispatchUiComponent = DispatchUiComponent::class.create(),
        ),
    )


}
