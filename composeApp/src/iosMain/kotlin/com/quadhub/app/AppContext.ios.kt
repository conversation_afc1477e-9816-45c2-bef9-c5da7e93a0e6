package com.quadhub.app

import com.quadhub.app.storage.CoreSharedStorage
import com.quadhub.core.log.ErrorLogger
import com.quadhub.core.network.CoreFileUploadConverter
import com.quadhub.core.sharedpref.AppSharedStorageProvider
import platform.Foundation.NSLog


internal actual class AppContext actual constructor(any: Any)

internal actual fun AppContext.getContext(): Any {
    return "context"
}

internal actual fun AppContext.buildSharedStorage(storageFileName: String): AppSharedStorageProvider {
    return CoreSharedStorage()
}

internal actual fun AppContext.buildErrorLogger(): ErrorLogger {

    return object : ErrorLogger {
        override fun log(message: String) {
            NSLog("Quadhub error %s", message)
        }

        override fun log(exception: Exception) {
            NSLog("Quadhub error %s", exception.message)
        }

        override fun log(throwable: Throwable) {

        }
    }
}

internal actual fun AppContext.buildFileConverter(): CoreFileUploadConverter {
    return object : CoreFileUploadConverter {
        override fun execute(filePath: String): ByteArray? {
            // TODO -> Provide the implementation
            return null
        }

        override fun audioFile(filePath: String): ByteArray? {
            return null
        }
    }
}