package com.quadhub.app

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.material.navigation.ModalBottomSheetLayout
import androidx.compose.material.navigation.rememberBottomSheetNavigator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import com.quadhub.app.navigation.MainNavigationDispatcher
import com.quadhub.app.ui.MainContract
import com.quadhub.app.ui.MainViewModel
import com.quadhub.compose.ui.component.scaffold.Scaffold
import com.quadhub.compose.ui.navigation.BottomBar
import com.quadhub.core.presentation.navigation.NavigationCommand
import com.quadhub.core.presentation.navigation.compose.NavContainer
import com.quadhub.core.presentation.navigation.compose.NavigatorHost
import com.quadhub.core.presentation.navigation.compose.rememberNavController
import kotlin.text.get

@Composable
internal fun RootScreen(
    viewModel: MainViewModel,
    navigatorHost: NavigatorHost.Root,
) {

    val viewState by remember { derivedStateOf { viewModel.state.value } }

    if (viewModel.isFirstIntent) {
        SideEffect {
            viewModel.emit(MainContract.Intent.Initial)
        }
    }

    Scaffold(
        bottomBar = {
            Column {
                viewState.bottomModel?.let { bottomModel ->
                    val items =
                        bottomModel.map { it.model }
                    BottomBar(
                        model = items,
                        isVisible = viewState.bottomState.show,
                        modifier = Modifier.windowInsetsPadding(WindowInsets.navigationBars),
                        onItemClick = { index ->
                            viewModel.emit(
                                MainContract.Intent.OnBottomNavBarClick(
                                    model = bottomModel[index],
                                )
                            )
                        }
                    )
                }
            }
        }
    ) { paddingValues ->
        val bottomSheetNavigator = rememberBottomSheetNavigator()
        val navController =
            rememberNavController(arrayOf(bottomSheetNavigator)) { _, destination, _ ->
                val cRoute = "${destination.parent?.route}/${destination.route}"
                viewModel.emit(
                    intent = MainContract.Intent.RouteChanged(
                        previousRoute = viewState.currentRoute ?: "subs",
                        route = cRoute
                    )
                )
            }

        viewState.startDestinationRoute?.let { startRoute ->
            var route by rememberSaveable { mutableStateOf(startRoute) }
            if (route != startRoute) route = startRoute
            ModalBottomSheetLayout(bottomSheetNavigator) {
                NavContainer(
                    modifier = Modifier
                        .fillMaxHeight()
                        .fillMaxWidth()
                        .padding(paddingValues = paddingValues),
                    startDestination = route,
                    navigatorHost = navigatorHost,
                    navHostController = navController,
                    pageTransition = viewState.pageTransition,
                )
            }
        }

        val navDispatcher =
            remember { MainNavigationDispatcher(navController = navController) }
        val command by rememberUpdatedState(newValue = viewState.navCommand)
        if (command != null) LaunchedEffect(command) {
            navDispatcher.navigateTo(command = command as NavigationCommand)
            viewModel.emit(intent = MainContract.Intent.ClearNav)
        }
    }

}