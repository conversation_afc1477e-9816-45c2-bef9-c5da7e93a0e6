package com.quadhub.compose.ui.component.toast


import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.DismissDirection
import androidx.compose.material.DismissValue
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.SwipeToDismiss
import androidx.compose.material.rememberDismissState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.quadhub.compose.ui.component.toast.internals.LocalToastColorProvider
import com.quadhub.compose.ui.component.toast.internals.ProvideToastColors
import com.quadhub.compose.ui.component.toast.internals.ToastColors
import com.quadhub.compose.ui.element.image.Icon
import com.quadhub.compose.ui.element.image.Icons
import com.quadhub.compose.ui.element.image.Image
import com.quadhub.compose.ui.element.text.Text
import com.quadhub.compose.ui.theme.Theme
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.time.Duration.Companion.seconds

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun ToastItem(
    model: ToastData,
    modifier: Modifier = Modifier,
    onDismissed: () -> Unit,
) {
    val scope = rememberCoroutineScope()
    var timeLeft by remember { mutableStateOf(3) }

    val dismissState = rememberDismissState(
        confirmStateChange = {
            if (it == DismissValue.DismissedToStart || it == DismissValue.DismissedToEnd) {
                onDismissed.invoke()
                true
            } else false
        },
    )

    LaunchedEffect(Unit) {
        while (timeLeft > 0) {
            delay(1.seconds)
            timeLeft--
        }

        scope.launch {
            dismissState.dismiss(DismissDirection.StartToEnd)
            onDismissed.invoke()
        }
    }

    ProvideToastColors(colors = provideToastColor(style = model.style)) {
        SwipeToDismiss(state = dismissState, background = { }) {
            Row(
                modifier = modifier
                    .clip(RoundedCornerShape(size = 8.dp))
                    .background(LocalToastColorProvider.current.backgroundColor).padding(
                        horizontal = Theme.spacing.x4,
                        vertical = Theme.spacing.x3
                    ),
                horizontalArrangement = Arrangement.spacedBy(Theme.spacing.x2)
            ) {
                Image(
                    image = model.leadingIcon,
                    modifier = Modifier.size(24.dp),
                    colorFilter = when (val iconColor = LocalToastColorProvider.current.iconColor) {
                        is Color -> ColorFilter.tint(color = iconColor)
                        else -> null
                    }
                )
                Column (modifier = Modifier.weight(1f)) {
                    model.title?.let {
                        Text(
                            clause = it,
                            style = Theme.typography.headingM.copy(
                                color = LocalToastColorProvider.current.titleColor,
                                fontSize = 14.sp
                            )
                        )
                        Spacer(modifier = Modifier.height(Theme.spacing.x1))
                    }
                    Text(
                        clause = model.description,
                        style = Theme.typography.bodyS.copy(color = LocalToastColorProvider.current.subtitleColor)
                    )
                }
                if (model.isDismissible) {
                    Spacer(modifier = Modifier.height(Theme.spacing.x2))
                    Icon(
                        image = Icons.close,
                        modifier = Modifier.size(24.dp)
                    )
                }
            }
        }
    }
}

@Composable
private fun provideToastColor(style: ToastStyle): ToastColors {
    return when (style) {
        ToastStyle.Error -> ToastColors(
            backgroundColor = Color(0xFF09101D),
            titleColor = Theme.colors.element.primaryOnDark,
            subtitleColor = Theme.colors.element.primaryOnDark,
            iconColor = Color(0xFFFF0000)
        )

        ToastStyle.Success -> ToastColors(
            backgroundColor = Color(0xFF09101D),
            titleColor = Theme.colors.element.primaryOnDark,
            subtitleColor = Theme.colors.element.primaryOnDark,
        )

        ToastStyle.Caution -> ToastColors(
            backgroundColor = Color(0xFF09101D),
            titleColor = Theme.colors.element.primaryOnDark,
            subtitleColor = Theme.colors.element.primaryOnDark,
            iconColor = Color(0xFFF9AC4D)
        )

        ToastStyle.System -> ToastColors(
            backgroundColor = Color(0xFF09101D),
            titleColor = Theme.colors.element.primaryOnDark,
            subtitleColor = Theme.colors.element.primaryOnDark,
            iconColor = null
        )
    }
}