package com.quadhub.app

import android.content.Context
import android.net.Uri
import android.os.Environment
import com.google.firebase.BuildConfig
import com.quadhub.core.network.CoreFileUploadConverter
import java.io.File

internal class FileUploaderImpl(private val appContext: Context) : CoreFileUploadConverter {

    override fun execute(filePath: String): ByteArray? {
            val fileUri = Uri.parse(filePath)
//        val fileUri = FileProvider.getUriForFile(appContext, getPackageName(), File(filePath))
        val byteArray = appContext.contentResolver.openInputStream(fileUri)?.buffered()
            ?.use { it.readBytes() }
        return byteArray
    }

    override fun audioFile(filePath: String): ByteArray {
        val audioFile = File(appContext.getExternalFilesDir(Environment.DIRECTORY_MUSIC), filePath)
        val byteArray = audioFile.inputStream().buffered().use { it.readBytes() }
        return byteArray
    }

    companion object {

        private fun getPackageName(): String {
            return when (BuildConfig.DEBUG) {
                true -> "com.quadhub.app.provider"
                false -> "com.quadhub.app.provider"
            }
        }
    }

}