package com.quadhub.compose.ui.component.button.model

import androidx.compose.runtime.Composable
import com.quadhub.compose.ui.component.button.internals.LocalButtonColorProvider
import com.quadhub.compose.ui.component.button.model.ButtonStyle.Regular
import com.quadhub.compose.ui.component.button.model.ButtonStyle.Small
import com.quadhub.compose.ui.theme.Theme

enum class ButtonState {
    Enabled, Disabled, Loading;
}

enum class ButtonStyle {
    Regular, Small
}

@Composable
internal fun ButtonStyle.toTextStyle(isEnabled: Boolean) = when (this) {
    Regular -> Theme.typography.body.copy(
        color = if (isEnabled) LocalButtonColorProvider.current.textColor else LocalButtonColorProvider.current.disabledTextColor
    )
    Small -> Theme.typography.headingS.copy(
        color = if (isEnabled) LocalButtonColorProvider.current.textColor else LocalButtonColorProvider.current.disabledTextColor
    )
}


@Composable
internal fun Boolean.toIconColor() = when(this) {
    true -> {
        LocalButtonColorProvider.current.contentColor
    }
    false -> {
        LocalButtonColorProvider.current.disabledContentColor
    }
}