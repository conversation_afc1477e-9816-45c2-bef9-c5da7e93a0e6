package com.quadhub.core.auth

import com.quadhub.core.sharedpref.AppSharedStorageProvider
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import me.tatarka.inject.annotations.Inject

interface OauthTokenProvider {
    fun getToken(): QaudhubAuthToken?
    fun setToken(token: QaudhubAuthToken)

    companion object {
        fun create(sharedPef: AppSharedStorageProvider): OauthTokenProvider =
            OauthTokenProviderImpl(sharedPef = sharedPef)
    }
}

internal class OauthTokenProviderImpl @Inject constructor(
    private val sharedPef: AppSharedStorageProvider
) : OauthTokenProvider {

    override fun getToken(): QaudhubAuthToken? {
        return sharedPef.get(OAUTH_TOKEN_KEY)?.let(Json::decodeFromString)
    }

    override fun setToken(token: QaudhubAuthToken) {
        val jsonString = Json.encodeToString(token)
        sharedPef.set(OAUTH_TOKEN_KEY, jsonString)
    }

    companion object {
        private const val OAUTH_TOKEN_KEY = "oauth_token"
    }

}