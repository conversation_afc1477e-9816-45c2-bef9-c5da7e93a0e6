package com.quadhub.app

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import com.quadhub.app.di.RootAppBuilder

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val appContext = AppContext(applicationContext)
        val app = RootAppBuilder.build(appContext, "https://devapi.quadhub.com")

        enableEdgeToEdge()

        setContent {
            CoreComposeApp(
                root = app.first,
                mainViewModelProvider = { app.second }
            )
        }
    }
}

