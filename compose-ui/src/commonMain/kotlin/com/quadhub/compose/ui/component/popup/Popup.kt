package com.quadhub.compose.ui.component.popup

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.quadhub.compose.ui.component.button.OutlineButton
import com.quadhub.compose.ui.component.button.PrimaryButton
import com.quadhub.compose.ui.component.popup.model.PopupEventType
import com.quadhub.compose.ui.component.popup.model.PopupModel
import com.quadhub.compose.ui.element.image.Image
import com.quadhub.compose.ui.element.text.Text
import com.quadhub.compose.ui.theme.Theme


@Composable
fun Popup(
    model: PopupModel,
    onDismissRequest: (PopupEventType) -> Unit
) {
    Dialog(
        onDismissRequest = {
            if(model.dismissible) {
                onDismissRequest.invoke(PopupEventType.Dismiss)
            }
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(16.dp))
                .background(Theme.colors.surface.primaryLight)
                .padding(Theme.spacing.x6),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {

            model.headerImage?.let {
                Image(image = it, modifier = Modifier.size(56.dp))
                Spacer(modifier = Modifier.height(Theme.spacing.x6))
            }
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(Theme.spacing.x2)
            ) {
                Text(
                    clause = model.title,
                    modifier = Modifier.fillMaxWidth(),
                    style = Theme.typography.headingM.copy(textAlign = TextAlign.Center)
                )
                Text(
                    clause = model.subtitle,
                    modifier = Modifier.fillMaxWidth(),
                    style = Theme.typography.bodyS.copy(textAlign = TextAlign.Center)
                )
            }
            Spacer(modifier = Modifier.height(Theme.spacing.x4))
            model.primaryBtn?.let {
                PrimaryButton(
                    model = it,
                    onClick = { onDismissRequest.invoke(PopupEventType.Primary) },
                    modifier = Modifier.fillMaxWidth()
                )
            }
            model.secondaryBtn?.let {
                Spacer(modifier = Modifier.height(Theme.spacing.x2))
                OutlineButton(
                    model = it,
                    onClick = { onDismissRequest.invoke(PopupEventType.Secondary) },
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }

    }

}