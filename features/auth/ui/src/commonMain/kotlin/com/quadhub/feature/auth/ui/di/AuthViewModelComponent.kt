package com.quadhub.feature.auth.ui.di

import com.quadhub.feature.auth.ui.navigation.ViewModelProviders
import com.quadhub.feature.auth.ui.screens.dispatch.DispatchViewModel
import com.quadhub.feature.auth.ui.screens.dispatch.di.DispatchUiComponent
import me.tatarka.inject.annotations.Component


@Component
internal abstract class AuthViewModelComponent(
    private val dispatchUiComponent: DispatchUiComponent,
) : ViewModelProviders {

    override val dispatchViewModel: () -> DispatchViewModel
        get() = { dispatchUiComponent.viewModel }

}
