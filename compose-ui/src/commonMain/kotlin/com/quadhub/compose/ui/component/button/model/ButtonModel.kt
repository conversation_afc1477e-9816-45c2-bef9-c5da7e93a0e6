package com.quadhub.compose.ui.component.button.model

import androidx.compose.runtime.Immutable
import com.quadhub.compose.ui.element.image.Image
import com.quadhub.compose.ui.element.text.Clause

@Immutable
data class ButtonModel(
    val title: Clause,
    val isVerified: Boolean = false,
    val leadingImage: Image? = null,
    val trailingImage: Image? = null
)

fun Clause.asButton() = ButtonModel(leadingImage = null, trailingImage = null, title = this)