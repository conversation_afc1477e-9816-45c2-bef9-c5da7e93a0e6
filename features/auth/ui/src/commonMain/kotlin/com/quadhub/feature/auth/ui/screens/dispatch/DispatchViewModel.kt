package com.quadhub.feature.auth.ui.screens.dispatch

import com.quadhub.core.presentation.mvi.FlowViewModelArgs
import com.quadhub.core.presentation.mvi.Interactor
import com.quadhub.core.presentation.mvi.LifecycleViewModel
import com.quadhub.core.presentation.mvi.Reducer
import com.quadhub.feature.auth.ui.screens.dispatch.DispatchContract.Intent
import com.quadhub.feature.auth.ui.screens.dispatch.DispatchContract.Result
import com.quadhub.feature.auth.ui.screens.dispatch.DispatchContract.ViewState
import me.tatarka.inject.annotations.Inject

internal class DispatchViewModel @Inject constructor(
    initialState: ViewState,
    interactor: Interactor<Intent, Result>,
    reducer: Reducer<ViewState, Result>
) : LifecycleViewModel<Intent, Result, ViewState>(
    args = FlowViewModelArgs(
        initialState = initialState,
        reducer = reducer,
        interactor = interactor
    )
)
