package com.quadhub.app

import androidx.compose.ui.window.ComposeUIViewController
import com.quadhub.app.di.RootAppBuilder
import platform.UIKit.UIEvent
import platform.UIKit.UIViewController


fun MainViewController(): UIViewController {

    val app = RootAppBuilder.build(AppContext(""), "",)

    return ComposeUIViewController {
        CoreComposeApp(
            root = app.first,
            mainViewModelProvider = { app.second }
        )
    }
}

class LokaComposeUIViewController : UIViewController() {

    override fun touchesBegan(touches: Set<*>, withEvent: UIEvent?) {
        super.touchesBegan(touches, withEvent)

    }

}
