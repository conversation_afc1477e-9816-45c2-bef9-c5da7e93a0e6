[versions]
agp = "8.7.3"
android-compileSdk = "35"
android-minSdk = "24"
android-targetSdk = "35"
androidx-activity = "1.10.1"
androidx-appcompat = "1.7.0"
androidx-constraintlayout = "2.2.1"
androidx-core = "1.16.0"
androidx-espresso = "3.6.1"
androidx-lifecycle = "2.9.0"
androidx-testExt = "1.2.1"
androidBiometrics = "1.4.0-alpha02"
appcompat = "1.7.0"
cameraCore = "1.4.1"
composeMultiplatform = "1.8.1"
composeNavigation = "2.8.0-alpha10"
coilCompose = "3.0.4"
firebaseBom = "33.7.0"
firebaseCrashlytics = "3.0.2"
googleGms = "4.4.2"
junit = "4.13.2"
jetbrainsCoroutineTest = "1.9.0"
kermitLogger = "2.0.4"
kotest = "5.9.1"
kotlin = "2.1.21"
kotlinInject = "0.7.2"
kotlinxDatetime = "0.6.1"
ksp = "2.1.0-1.0.29"
ktor = "3.0.3"
materialNavigation = "1.7.0-beta02"
mockk = "1.12.5"
mockative = "3.0.1"
parcelizeDarwin = "0.2.4"
roomCommonJvm = "2.7.1"
sqlDelight = "2.0.2"
sqlcipherAndroid = "4.6.1"
tensorflowLite = "2.14.0"
tensorflowLiteSupport = "0.4.4"

[libraries]
kotlin-test = { module = "org.jetbrains.kotlin:kotlin-test", version.ref = "kotlin" }
kotlin-testJunit = { module = "org.jetbrains.kotlin:kotlin-test-junit", version.ref = "kotlin" }
junit = { module = "junit:junit", version.ref = "junit" }
androidx-core-ktx = { module = "androidx.core:core-ktx", version.ref = "androidx-core" }
androidx-testExt-junit = { module = "androidx.test.ext:junit", version.ref = "androidx-testExt" }
androidx-espresso-core = { module = "androidx.test.espresso:espresso-core", version.ref = "androidx-espresso" }
androidx-appcompat = { module = "androidx.appcompat:appcompat", version.ref = "androidx-appcompat" }
androidx-constraintlayout = { module = "androidx.constraintlayout:constraintlayout", version.ref = "androidx-constraintlayout" }
androidx-activity-compose = { module = "androidx.activity:activity-compose", version.ref = "androidx-activity" }
androidx-lifecycle-viewmodel = { module = "org.jetbrains.androidx.lifecycle:lifecycle-viewmodel", version.ref = "androidx-lifecycle" }
androidx-lifecycle-runtime-compose = { group = "org.jetbrains.androidx.lifecycle", name = "lifecycle-runtime-compose", version.ref = "androidx-lifecycle" }

#CameraX libraries
androidx-camera-camera2 = { module = "androidx.camera:camera-camera2", version.ref = "cameraCore" }
androidx-camera-core = { module = "androidx.camera:camera-core", version.ref = "cameraCore" }
androidx-camera-lifecycle = { module = "androidx.camera:camera-lifecycle", version.ref = "cameraCore" }
androidx-camera-video = { module = "androidx.camera:camera-video", version.ref = "cameraCore" }
androidx-camera-view = { module = "androidx.camera:camera-view", version.ref = "cameraCore" }

#Biometrics
android-biometrics = { module = "androidx.biometric:biometric-ktx", version.ref= "androidBiometrics" }

#Jetbrains coroutine
coroutine-core = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "jetbrainsCoroutineTest" }

#Compose navigation
navigation-compose = { module = "org.jetbrains.androidx.navigation:navigation-compose", version.ref = "composeNavigation" }
material-navigation = { module = "org.jetbrains.compose.material:material-navigation", version.ref = "materialNavigation" }

#Firebase libraries
firebase-analytics = { module = "com.google.firebase:firebase-analytics" }
firebase-crashlytics = { module = "com.google.firebase:firebase-crashlytics" }
firebase-bom = { module = "com.google.firebase:firebase-bom", version.ref = "firebaseBom" }

#kotlin inject
kotlin-inject-compiler = { module = "me.tatarka.inject:kotlin-inject-compiler-ksp", version.ref = "kotlinInject" }
kotlin-inject-runtime = { module = "me.tatarka.inject:kotlin-inject-runtime", version.ref = "kotlinInject" }
kotlin-inject-runtime-kmp = { module = "me.tatarka.inject:kotlin-inject-runtime-kmp", version.ref = "kotlinInject" }

kotlinx-datetime = { module = "org.jetbrains.kotlinx:kotlinx-datetime", version.ref = "kotlinxDatetime" }


#Coil libraries
coil-compose = { module = "io.coil-kt.coil3:coil-compose", version.ref = "coilCompose" }
coil-ktor = { module = "io.coil-kt.coil3:coil-network-ktor3", version.ref = "coilCompose" }

#kermit logger
kermit-logger = { module = "co.touchlab:kermit", version.ref = "kermitLogger" }

#Ktor libraries
ktor-client-core = { module = "io.ktor:ktor-client-core", version.ref = "ktor" }
ktor-client-okhttp = { module = "io.ktor:ktor-client-okhttp", version.ref = "ktor" }
ktor-client-darwin = { module = "io.ktor:ktor-client-darwin", version.ref = "ktor" }
ktor-client-android = { module = "io.ktor:ktor-client-android", version.ref = "ktor" }
ktor-client-cio = { module = "io.ktor:ktor-client-cio", version.ref = "ktor" }
ktor-client-websockets = { module = "io.ktor:ktor-client-websockets", version.ref = "ktor" }
ktor-serialization-gson = { module = "io.ktor:ktor-serialization-gson", version.ref = "ktor" }
ktor-serialization-kotlinx-json = { module = "io.ktor:ktor-serialization-kotlinx-json", version.ref = "ktor" }
ktor-content-negotiation = { module = "io.ktor:ktor-client-content-negotiation", version.ref = "ktor" }
ktor-client-logging = { module = "io.ktor:ktor-client-logging", version.ref = "ktor" }


mockk = { module = "io.mockk:mockk", version.ref = "mockk" }
mockk-common = { module = "io.mockk:mockk-common", version.ref = "mockk" }

#Parcelize
parcelize-darwin = { module = "com.arkivanov.parcelize.darwin:runtime", version.ref = "parcelizeDarwin" }


sqlcipher-android = { module = "net.zetetic:sqlcipher-android", version.ref = "sqlcipherAndroid" }

#SQLDelight
sql-delight-android = { module = "app.cash.sqldelight:android-driver", version.ref = "sqlDelight" }
sql-delight-coroutine = { module = "app.cash.sqldelight:coroutines-extensions", version.ref = "sqlDelight" }
sql-delight-native = { module = "app.cash.sqldelight:native-driver", version.ref = "sqlDelight" }
sql-delight-driver = { module = "app.cash.sqldelight:sqlite-driver", version.ref = "sqlDelight" }

#tensorflow
tensorflow-lite = { module = "org.tensorflow:tensorflow-lite", version.ref = "tensorflowLite" }
tensorflow-lite-support = { module = "org.tensorflow:tensorflow-lite-support", version.ref = "tensorflowLiteSupport" }

[plugins]
androidApplication = { id = "com.android.application", version.ref = "agp" }
androidLibrary = { id = "com.android.library", version.ref = "agp" }
composeMultiplatform = { id = "org.jetbrains.compose", version.ref = "composeMultiplatform" }
compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
firebase-crashlytics = { id = "com.google.firebase.crashlytics", version.ref = "firebaseCrashlytics" }
googleService = { id = "com.google.gms.google-services", version.ref = "googleGms" }
jetbrainsCompose = { id = "org.jetbrains.compose", version.ref = "composeMultiplatform" }
kotlinAndroid = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlinCocoapods = { id = "org.jetbrains.kotlin.native.cocoapods", version.ref = "kotlin" }
kotlinMultiplatform = { id = "org.jetbrains.kotlin.multiplatform", version.ref = "kotlin" }
kotlinxSerialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
mockative = { id = "io.mockative", version.ref = "mockative" }
sql-delight = { id = "app.cash.sqldelight", version.ref = "sqlDelight" }
