package com.quadhub.app.di

import com.quadhub.core.presentation.feature.FeatureLauncher
import com.quadhub.core.presentation.navigation.compose.NavigatorHost
import com.quadhub.app.navigation.MainNavigatorHost
import com.quadhub.core.discopes.SingletonScope
import com.quadhub.feature.auth.ui.di.AuthModule
import me.tatarka.inject.annotations.Component
import me.tatarka.inject.annotations.Provides


@SingletonScope
@Component
internal abstract class ComposeAppComponent(
    private val authModule: AuthModule,
) {
    abstract val root: NavigatorHost.Root

    protected val MainNavigatorHost.bind: NavigatorHost.Root
        @Provides get() = this

    @Provides
    fun provideFeatures(): Map<String, FeatureLauncher> = authModule.features
}
