package com.quadhub.feature.auth.ui.screens.dispatch

import me.tatarka.inject.annotations.Inject
import com.quadhub.feature.auth.ui.screens.dispatch.DispatchContract.Intent
import com.quadhub.feature.auth.ui.screens.dispatch.DispatchContract.Result
import com.quadhub.feature.auth.ui.screens.dispatch.DispatchContract.Mapper

internal class DispatchMapper @Inject constructor() : Mapper {

    override fun toResult(intent: Intent.Navigation): Result.OnNavigationCommandUpdated {
        return when (intent) {
            Intent.Navigation.Done -> Result.OnNavigationCommandUpdated(null)
        }
    }
}